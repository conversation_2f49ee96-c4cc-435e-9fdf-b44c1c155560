<script setup lang="ts">
import EditorTool from 'src/components/common/EditorTool.vue';
import { computed, ref, watch, nextTick } from 'vue';
import type { ImageBody, ItemBlock } from 'src/types/models';
import type { CSSProperties } from 'vue';
import ItemBlockFooter from './ItemBlockFooter.vue';
import FloatImageBtn from '../FloatImageBtn.vue';
import { useGlobalStore } from 'src/stores/global';
import { ImageBodyService } from 'src/services/asm/imageBodyService';
const props = defineProps<{
  itemBlock: ItemBlock;
}>();

const emit = defineEmits(['focus-fab', 'duplicate', 'delete', 'update:image']);
const globalStore = useGlobalStore();
const selectedFile = ref<File | null>(null);
const itemBlockId = ref(props.itemBlock.id || 0);
const imageId = ref(props.itemBlock.imageBody?.id || 0);

// Initialize imageText - treat empty string and null as undefined to show placeholder
const initialImageText = props.itemBlock.imageBody?.imageText;
const imageText = ref(
  initialImageText === '' || initialImageText === null ? undefined : initialImageText,
);
const lastSavedImageText = ref(imageText.value);

// Watch for changes in itemBlock props to update local state
watch(
  () => props.itemBlock.imageBody?.imageText,
  (newImageText) => {
    console.log('🔄 ImageBlock watcher triggered:', {
      newImageText,
      currentImageText: imageText.value,
      itemBlockId: props.itemBlock.id,
    });

    // Treat empty string and null as undefined to show placeholder
    const processedText = newImageText === '' || newImageText === null ? undefined : newImageText;
    if (processedText !== imageText.value) {
      console.log('📝 Updating imageText:', { from: imageText.value, to: processedText });
      imageText.value = processedText;
      lastSavedImageText.value = processedText;
    }
  },
  { immediate: true },
);

// Watch for changes in imageBody ID to update local state
watch(
  () => props.itemBlock.imageBody?.id,
  (newImageId) => {
    if (newImageId !== undefined) {
      imageId.value = newImageId;
    }
  },
  { immediate: true },
);

// Watch for changes in itemBlock ID to update local state
watch(
  () => props.itemBlock.id,
  (newItemBlockId) => {
    if (newItemBlockId !== undefined) {
      itemBlockId.value = newItemBlockId;
    }
  },
  { immediate: true },
);

// Watch for changes in image dimensions to trigger container adaptation
watch(
  () => [props.itemBlock.imageBody?.imageWidth, props.itemBlock.imageBody?.imageHeight],
  ([newWidth, newHeight], [oldWidth, oldHeight]) => {
    if (newWidth !== oldWidth || newHeight !== oldHeight) {
      console.log('🔄 Image dimensions changed, container will adapt:', {
        from: { width: oldWidth, height: oldHeight },
        to: { width: newWidth, height: newHeight },
        itemBlockId: props.itemBlock.id,
      });
      // The containerStyle computed property will automatically recalculate
      // due to its reactive dependencies on imageWidth and imageHeight
    }
  },
  { immediate: false },
);

const imgUrl = computed(() => {
  const imagePath = props.itemBlock.imageBody?.imagePath || '';
  console.log('🖼️ ImageBlock imgUrl computed:', {
    itemBlockId: props.itemBlock.id,
    imageBodyId: props.itemBlock.imageBody?.id,
    imagePath: props.itemBlock.imageBody?.imagePath,
    imagePathType: typeof props.itemBlock.imageBody?.imagePath,
    computedImgUrl: imagePath,
  });
  return imagePath;
});

const imageStyle = computed<CSSProperties>(() => {
  const body = props.itemBlock.imageBody;

  // Use effective dimensions (local overrides or props)
  const currentWidth = effectiveImageWidth.value;
  const currentHeight = effectiveImageHeight.value;

  // If no dimensions are available, use auto sizing with reasonable constraints
  if (!body || !currentWidth || !currentHeight || currentWidth <= 0 || currentHeight <= 0) {
    return {
      width: 'auto',
      height: 'auto',
      objectFit: 'contain',
      display: 'block', // optional: removes inline whitespace
    };
  }

  const originalWidth = currentWidth;
  const originalHeight = currentHeight;

  // Define maximum container constraints for responsive design
  const maxContainerWidth = 1200; // Maximum width for very large images
  const maxContainerHeight = 800; // Maximum height for very tall images
  const minDisplayWidth = 200; // Minimum width for very small images
  const minDisplayHeight = 150; // Minimum height for very small images

  // Calculate aspect ratio
  const aspectRatio = originalWidth / originalHeight;

  let finalWidth = originalWidth;
  let finalHeight = originalHeight;

  // Handle very large images - scale down proportionally
  if (originalWidth > maxContainerWidth || originalHeight > maxContainerHeight) {
    const widthScale = maxContainerWidth / originalWidth;
    const heightScale = maxContainerHeight / originalHeight;
    const scale = Math.min(widthScale, heightScale);

    finalWidth = Math.round(originalWidth * scale);
    finalHeight = Math.round(originalHeight * scale);

    console.log('📏 Scaling down large image:', {
      original: { width: originalWidth, height: originalHeight },
      scaled: { width: finalWidth, height: finalHeight },
      scale: scale,
      aspectRatio: aspectRatio,
    });
  }
  // Handle very small images - ensure minimum usable size
  else if (originalWidth < minDisplayWidth || originalHeight < minDisplayHeight) {
    const widthScale = minDisplayWidth / originalWidth;
    const heightScale = minDisplayHeight / originalHeight;
    const scale = Math.max(widthScale, heightScale);

    // Only scale up if the image is significantly smaller than minimum
    if (scale > 1.5) {
      finalWidth = Math.round(originalWidth * scale);
      finalHeight = Math.round(originalHeight * scale);

      console.log('📏 Scaling up small image:', {
        original: { width: originalWidth, height: originalHeight },
        scaled: { width: finalWidth, height: finalHeight },
        scale: scale,
        aspectRatio: aspectRatio,
      });
    }
  }

  console.log('🎯 Final image dimensions:', {
    original: { width: originalWidth, height: originalHeight },
    display: { width: finalWidth, height: finalHeight },
    aspectRatio: aspectRatio,
  });

  return {
    width: `${finalWidth}px`,
    height: `${finalHeight}px`,
    objectFit: 'contain' as const,
    display: 'block', // ทำให้รูป block-level เพื่อไม่ให้มีช่องว่าง inline
    margin: '20px', // ลบ margin รอบภาพ
    maxWidth: 'none', // ไม่ให้ขยายตาม container
    maxHeight: 'none',
  };
});

// Simplified container style that adapts naturally to content
const containerStyle = computed<CSSProperties>(() => {
  return {
    cursor: 'pointer',
    minHeight: '200px', // Minimal height for empty state
    transition: 'box-shadow 0.2s ease',
  };
});

async function performSaveImageText() {
  const imageBody = props.itemBlock.imageBody;
  const isCreate = !imageBody?.id;

  // Convert undefined to empty string for comparison
  const currentText = imageText.value || '';
  const lastSavedText = lastSavedImageText.value || '';

  console.log('💾 performSaveImageText called:', {
    isCreate,
    currentText,
    lastSavedText,
    hasFile: !!selectedFile.value,
    imageBodyId: imageBody?.id,
    existingImagePath: imageBody?.imagePath,
  });

  if (currentText.trim() === lastSavedText.trim() && !selectedFile.value) {
    console.log('⏭️ Skipping save - no changes detected');
    return;
  }

  try {
    globalStore.startSaveOperation(isCreate ? 'Creating...' : 'Saving...');

    const service = new ImageBodyService();
    let updated: ImageBody;

    if (isCreate) {
      updated = await service.createImageBody(
        {
          itemBlockId: props.itemBlock.id,
          imageText: currentText, // Use converted text value
          id: itemBlockId.value,
        },
        selectedFile.value!,
      );
    } else {
      // Check if we're updating with a file or just text
      if (selectedFile.value) {
        // Update with new file
        updated = await service.updateImageBody(
          imageBody.id,
          {
            itemBlockId: props.itemBlock.id,
            imageText: currentText, // Use converted text value
            id: imageId.value,
          },
          selectedFile.value,
        );
      } else {
        // Text-only update to preserve existing image
        console.log('📝 Performing text-only update to preserve image', {
          imageBodyId: imageBody.id,
          currentText,
          existingImagePath: imageBody.imagePath,
          existingImageWidth: imageBody.imageWidth,
          existingImageHeight: imageBody.imageHeight,
        });
        updated = await service.updateImageTextOnly(
          imageBody.id,
          currentText,
          imageBody.imagePath || undefined,
          imageBody.imageWidth,
          imageBody.imageHeight,
        );
      }
    }

    // Handle backend response properly
    console.log('✅ Save successful, backend response:', {
      imageText: updated.imageText,
      imagePath: updated.imagePath,
      id: updated.id,
    });

    // Backend returns null for empty text, so handle both null and empty string
    const backendImageText = updated.imageText;
    if (backendImageText !== undefined && backendImageText !== null && backendImageText !== '') {
      // Backend returned actual text content
      console.log('📝 Setting imageText from backend:', backendImageText);
      imageText.value = backendImageText;
      lastSavedImageText.value = backendImageText;
    } else {
      // Backend returned null/undefined/empty, treat as undefined for UI to show placeholder
      console.log('🔄 Backend returned empty text, setting to undefined for placeholder');
      imageText.value = undefined;
      lastSavedImageText.value = undefined;
    }
    selectedFile.value = null; // เคลียร์ไฟล์หลังบันทึกเสร็จ

    globalStore.completeSaveOperation(
      true,
      isCreate ? 'Created successfully' : 'Saved successfully',
    );

    // if (isCreate) {
    //   emit('update:image', updated);
    // }
  } catch {
    globalStore.completeSaveOperation(false, isCreate ? 'Create failed' : 'Save failed');
  }
}

// Handle click on ImageBlock to focus FAB
const handleImageBlockClick = (event: Event) => {
  // Prevent focusing FAB if clicking on interactive elements
  const target = event.target as HTMLElement;

  // Check if the click is on an interactive element that should not trigger FAB focus
  if (
    target.closest('.q-btn') || // Any button
    target.closest('.q-input') || // Any input field
    target.closest('.q-editor') || // Editor tool
    target.closest('.pixel-image-position') || // FloatImageBtn
    target.closest('[contenteditable]') // Any contenteditable element
  ) {
    return;
  }

  // Emit focus-fab event to position the FAB on this ImageBlock
  emit('focus-fab', props.itemBlock.id);
};

// Reactive local dimensions that override props when updated
const localImageWidth = ref<number | null>(null);
const localImageHeight = ref<number | null>(null);

// Computed properties that use local dimensions if available, otherwise fall back to props
const effectiveImageWidth = computed(() => {
  return localImageWidth.value ?? props.itemBlock.imageBody?.imageWidth ?? 0;
});

const effectiveImageHeight = computed(() => {
  return localImageHeight.value ?? props.itemBlock.imageBody?.imageHeight ?? 0;
});

// Watch for prop changes and reset local dimensions when backend data is updated
watch(
  () => [props.itemBlock.imageBody?.imageWidth, props.itemBlock.imageBody?.imageHeight],
  ([newWidth, newHeight], [oldWidth, oldHeight]) => {
    // Only reset local dimensions if the props actually changed from backend
    // and we don't have pending local changes
    if (
      (newWidth !== oldWidth || newHeight !== oldHeight) &&
      newWidth !== localImageWidth.value &&
      newHeight !== localImageHeight.value
    ) {
      console.log('🔄 Props dimensions changed, resetting local overrides:', {
        oldProps: { width: oldWidth, height: oldHeight },
        newProps: { width: newWidth, height: newHeight },
        localOverrides: { width: localImageWidth.value, height: localImageHeight.value },
      });

      // Reset local overrides to sync with backend
      localImageWidth.value = null;
      localImageHeight.value = null;
    }
  },
  { deep: true },
);

// Handle dimensions updated from FloatImageBtn
const handleDimensionsUpdated = (dimensions: { width: number; height: number }) => {
  console.log('🎯 Image dimensions updated from FloatImageBtn:', {
    itemBlockId: props.itemBlock.id,
    imageBodyId: props.itemBlock.imageBody?.id,
    newDimensions: dimensions,
    previousDimensions: {
      width: props.itemBlock.imageBody?.imageWidth,
      height: props.itemBlock.imageBody?.imageHeight,
    },
    currentImagePath: props.itemBlock.imageBody?.imagePath,
    currentImagePathType: typeof props.itemBlock.imageBody?.imagePath,
    fullImageBody: props.itemBlock.imageBody,
  });

  // CRITICAL: Update local reactive dimensions immediately to prevent image disappearance
  localImageWidth.value = dimensions.width;
  localImageHeight.value = dimensions.height;

  console.log('✅ Local dimensions updated immediately:', {
    itemBlockId: props.itemBlock.id,
    localDimensions: {
      width: localImageWidth.value,
      height: localImageHeight.value,
    },
    effectiveDimensions: {
      width: effectiveImageWidth.value,
      height: effectiveImageHeight.value,
    },
  });

  // Emit to parent component for store updates
  emit('update:image', {
    itemBlockId: props.itemBlock.id,
    dimensions: dimensions,
  });

  // Force Vue reactivity update
  void nextTick(() => {
    console.log('🔄 Forced reactivity update completed');
  });
};
</script>

<template>
  <q-card
    class="q-pa-lg image-block-container"
    :style="containerStyle"
    @click="handleImageBlockClick"
  >
    <!-- Content area that grows to fill available space -->
    <div class="image-block-content">
      <EditorTool
        class="q-mt-sm"
        label="ข้อความ..."
        :initialValue="imageText || ''"
        @update:content="(val) => (imageText = val || undefined)"
        @blur="performSaveImageText"
      />

      <!-- Image section with proper spacing -->
      <div v-if="imgUrl.length > 0" class="image-section">
        <div class="image-container">
          <div class="image-wrapper">
            <img :src="imgUrl" :style="imageStyle" />
          </div>
          <FloatImageBtn
            class="pixel-image-position"
            :item-block="props.itemBlock"
            :key="`fab-${props.itemBlock.id}-${effectiveImageWidth}-${effectiveImageHeight}`"
            @dimensions-updated="handleDimensionsUpdated"
          />
        </div>
      </div>
    </div>

    <!-- Footer section that sticks to bottom -->
    <div class="image-block-footer">
      <q-separator></q-separator>
      <div class="footer-content">
        <ItemBlockFooter label="ข้อความ" @delete="$emit('delete')" />
      </div>
    </div>
  </q-card>
</template>
<style scoped>
/* Fixed positioning for FloatImageBtn - always at top-left of image */
.pixel-image-position {
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  z-index: 10;
  cursor: pointer;
}

.image-wrapper {
  display: inline-block;
  /* ขนาดพอดีกับรูป */
  padding: 0;
  margin: 0;
  /* แสดงให้เห็นขอบ */
  line-height: 0;
  /* ป้องกันช่องว่าง inline */
}

/* Enhanced image container for responsive image display */
.image-container {
  position: relative;
  display: inline-block;
  /* Contain the image and overlay */
  margin: 0 auto;
  /* Center the container */
  max-width: 100%;
  /* Ensure container doesn't exceed parent width */
  overflow: visible;
  margin-top: 20px;
  /* Allow FloatImageBtn to be visible outside bounds */
}

/* Image section with proper spacing */
.image-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;

  /* Add some vertical spacing around image */
  flex-grow: 1;
  /* Allow this section to grow */
}

/* Enhanced styles for the clickable ImageBlock container */
.image-block-container {
  transition: box-shadow 0.2s ease;
  display: flex;
  flex-direction: column;
  min-height: 200px;
  /* Minimal height for empty state */
}

.image-block-container:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Content area that grows to fill available space */
.image-block-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

/* Footer section that sticks to bottom */
.image-block-footer {
  flex-shrink: 0;
  /* Prevent footer from shrinking */
  margin-top: auto;
  /* Push footer to bottom */
}

.footer-content {
  padding: 8px 0;
  /* Minimal padding for footer */
  min-height: 50px;
  /* Ensure consistent footer height */
  display: flex;
  align-items: center;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .image-container {
    max-width: 95%;
  }

  .image-section {
    padding: 12px 0;
  }
}

@media (max-width: 480px) {
  .image-container {
    max-width: 90%;
  }

  .image-section {
    padding: 8px 0;
  }

  .footer-content {
    min-height: 45px;
  }
}
</style>
